<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Country;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Passport\Passport;

class MerchantRemittanceApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $merchant;
    protected $sendCountry;
    protected $receiveCountry;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a merchant user
        $this->merchant = User::factory()->create([
            'merchant' => 1,
            'status' => 1,
            'email_verification' => 1,
            'sms_verification' => 1,
            'identity_verify' => 2, // KYC verified
        ]);

        // Create test countries
        $this->sendCountry = Country::factory()->create([
            'name' => 'United States',
            'code' => 'USD',
            'send_from' => 1,
            'send_to' => 0,
            'status' => 1,
            'rate' => 1.0,
            'minimum_amount' => 10,
            'maximum_amount' => 10000,
        ]);

        $this->receiveCountry = Country::factory()->create([
            'name' => 'Nigeria',
            'code' => 'NGN',
            'send_from' => 0,
            'send_to' => 1,
            'status' => 1,
            'rate' => 750.0,
            'minimum_amount' => 1000,
            'maximum_amount' => 5000000,
        ]);

        // Create a test service
        $this->service = Service::factory()->create([
            'name' => 'Bank Transfer',
            'status' => 1,
        ]);
    }

    /** @test */
    public function it_can_get_available_send_currencies()
    {
        Passport::actingAs($this->merchant);

        $response = $this->getJson('/api/merchant/remittance/send-currencies');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'currencies' => [
                            '*' => [
                                'id',
                                'name',
                                'code',
                                'minimum_amount',
                                'maximum_amount',
                                'rate',
                                'flag'
                            ]
                        ],
                        'total_count'
                    ]
                ])
                ->assertJson([
                    'status' => true
                ]);
    }

    /** @test */
    public function it_can_get_available_receive_currencies()
    {
        Passport::actingAs($this->merchant);

        $response = $this->getJson('/api/merchant/remittance/receive-currencies');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'currencies' => [
                            '*' => [
                                'id',
                                'name',
                                'code',
                                'minimum_amount',
                                'maximum_amount',
                                'rate',
                                'flag'
                            ]
                        ],
                        'total_count',
                        'merchant_country_id'
                    ]
                ])
                ->assertJson([
                    'status' => true
                ]);
    }

    /** @test */
    public function it_can_calculate_remittance_fees()
    {
        Passport::actingAs($this->merchant);

        $requestData = [
            'amount' => 100,
            'send_currency_id' => $this->sendCountry->id,
            'receive_currency_id' => $this->receiveCountry->id,
            'service_id' => $this->service->id,
        ];

        $response = $this->postJson('/api/merchant/remittance/calculate-fees', $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'send_amount',
                        'send_currency',
                        'receive_currency',
                        'exchange_rate',
                        'service_fees',
                        'total_payable',
                        'recipient_gets',
                        'merchant_commission',
                        'service_name',
                        'calculation_details'
                    ]
                ])
                ->assertJson([
                    'status' => true
                ]);
    }

    /** @test */
    public function it_rejects_unauthorized_access()
    {
        // Test without authentication
        $response = $this->getJson('/api/merchant/remittance/send-currencies');
        $response->assertStatus(401);

        // Test with non-merchant user
        $regularUser = User::factory()->create(['merchant' => 0]);
        Passport::actingAs($regularUser);

        $response = $this->getJson('/api/merchant/remittance/send-currencies');
        $response->assertStatus(403)
                ->assertJson([
                    'status' => false,
                    'message' => 'Unauthorized merchant access'
                ]);
    }

    /** @test */
    public function it_validates_calculate_fees_request()
    {
        Passport::actingAs($this->merchant);

        // Test with missing required fields
        $response = $this->postJson('/api/merchant/remittance/calculate-fees', []);
        $response->assertStatus(422);

        // Test with invalid amount
        $response = $this->postJson('/api/merchant/remittance/calculate-fees', [
            'amount' => -10,
            'send_currency_id' => $this->sendCountry->id,
            'receive_currency_id' => $this->receiveCountry->id,
            'service_id' => $this->service->id,
        ]);
        $response->assertStatus(422);

        // Test with non-existent currency
        $response = $this->postJson('/api/merchant/remittance/calculate-fees', [
            'amount' => 100,
            'send_currency_id' => 99999,
            'receive_currency_id' => $this->receiveCountry->id,
            'service_id' => $this->service->id,
        ]);
        $response->assertStatus(422);
    }
}

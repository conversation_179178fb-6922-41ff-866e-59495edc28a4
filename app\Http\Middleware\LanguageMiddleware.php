<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $languageCode = $this->getCode();
        $direction = $this->getDirection();

        session()->put('trans', $languageCode);
        session()->put('rtl', $direction);

        app()->setLocale($languageCode);
        return $next($request);
    }

    public function getCode()
    {
        try {
            // 1. For authenticated users, check their language preference
            // Check regular user authentication
            if (Auth::check() && Auth::user()->language_id) {
                $userLanguage = Language::where('id', Auth::user()->language_id)
                    ->where('is_active', 1)
                    ->first();
                if ($userLanguage) {
                    return $userLanguage->short_name;
                }
            }

            // Check admin authentication
            if (Auth::guard('admin')->check() && Auth::guard('admin')->user()->language_id) {
                $adminLanguage = Language::where('id', Auth::guard('admin')->user()->language_id)
                    ->where('is_active', 1)
                    ->first();
                if ($adminLanguage) {
                    return $adminLanguage->short_name;
                }
            }

            // 2. Check session for guest users or temporary language switches
            if (session()->has('trans')) {
                $sessionLang = session('trans');
                // Verify the session language is still valid
                $validLang = Language::where('short_name', $sessionLang)
                    ->where('is_active', 1)
                    ->first();
                if ($validLang) {
                    return $sessionLang;
                }
            }

            // 3. Fall back to default language
            $defaultLanguage = Language::where('is_active', 1)
                ->where('default_status', 1)
                ->first();
            if ($defaultLanguage) {
                return $defaultLanguage->short_name;
            }

            // 4. Final fallback to first active language
            $firstLanguage = Language::where('is_active', 1)->first();
            return $firstLanguage ? $firstLanguage->short_name : 'en';

        } catch (\Exception $e) {
            return 'en';
        }
    }

    public function getDirection()
    {
        try {
            // Get the current language code
            $currentLangCode = $this->getCodeForDirection();

            // Find the language and return its RTL setting
            $language = Language::where('short_name', $currentLangCode)
                ->where('is_active', 1)
                ->first();

            return $language ? $language->rtl : 0;

        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getCodeForDirection()
    {
        // 1. For authenticated users, check their language preference
        // Check regular user authentication
        if (Auth::check() && Auth::user()->language_id) {
            $userLanguage = Language::where('id', Auth::user()->language_id)
                ->where('is_active', 1)
                ->first();
            if ($userLanguage) {
                return $userLanguage->short_name;
            }
        }

        // Check admin authentication
        if (Auth::guard('admin')->check() && Auth::guard('admin')->user()->language_id) {
            $adminLanguage = Language::where('id', Auth::guard('admin')->user()->language_id)
                ->where('is_active', 1)
                ->first();
            if ($adminLanguage) {
                return $adminLanguage->short_name;
            }
        }

        // 2. Check session
        if (session()->has('trans')) {
            return session('trans');
        }

        // 3. Fall back to default language
        $defaultLanguage = Language::where('is_active', 1)
            ->where('default_status', 1)
            ->first();
        if ($defaultLanguage) {
            return $defaultLanguage->short_name;
        }

        // 4. Final fallback
        $firstLanguage = Language::where('is_active', 1)->first();
        return $firstLanguage ? $firstLanguage->short_name : 'en';
    }

}

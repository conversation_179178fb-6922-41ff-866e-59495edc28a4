<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddComprehensiveIdempotencyColumnsToTransactions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Add new foreign key columns for comprehensive idempotency
            // All columns are nullable for backward compatibility
            
            // Payout operations (withdrawals, payouts)
            $table->bigInteger('payout_id')->nullable()->after('send_money_id');
            
            // Agent transfer operations
            $table->bigInteger('agent_transfer_id')->nullable()->after('payout_id');
            
            // Fund operations (payment gateway deposits)
            $table->bigInteger('fund_id')->nullable()->after('agent_transfer_id');
            
            // Add foreign key constraints with restrict to prevent deletion
            // This ensures parent records cannot be deleted if they have associated transactions
            
            $table->foreign('payout_id')->references('id')->on('payouts')->onDelete('restrict');
            $table->foreign('agent_transfer_id')->references('id')->on('agent_transfers')->onDelete('restrict');
            $table->foreign('fund_id')->references('id')->on('funds')->onDelete('restrict');
            
            // Create unique composite indexes for idempotency protection
            // These prevent duplicate transactions for each operation type
            
            $table->unique(['payout_id', 'transaction_type'], 'unique_payout_transaction_type');
            $table->unique(['agent_transfer_id', 'transaction_type'], 'unique_agent_transfer_transaction_type');
            $table->unique(['fund_id', 'transaction_type'], 'unique_fund_transaction_type');
            
            // Add performance indexes for efficient querying
            $table->index(['payout_id', 'user_id'], 'idx_payout_user');
            $table->index(['agent_transfer_id', 'user_id'], 'idx_agent_transfer_user');
            $table->index(['fund_id', 'user_id'], 'idx_fund_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Drop indexes first
            $table->dropUnique('unique_payout_transaction_type');
            $table->dropUnique('unique_agent_transfer_transaction_type');
            $table->dropUnique('unique_fund_transaction_type');
            
            $table->dropIndex('idx_payout_user');
            $table->dropIndex('idx_agent_transfer_user');
            $table->dropIndex('idx_fund_user');
            
            // Drop foreign key constraints
            $table->dropForeign(['payout_id']);
            $table->dropForeign(['agent_transfer_id']);
            $table->dropForeign(['fund_id']);
            
            // Drop columns
            $table->dropColumn(['payout_id', 'agent_transfer_id', 'fund_id']);
        });
    }
}

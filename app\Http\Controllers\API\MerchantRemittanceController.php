<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\Service;
use App\Models\CountryService;
use Illuminate\Http\Request;
use App\Http\Traits\Notify;
use Illuminate\Support\Facades\Validator;

class MerchantRemittanceController extends Controller
{
    use Notify;

    /**
     * Get available send currencies for remittance
     * Returns all active currencies that can be used as source currencies
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableSendCurrencies()
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        try {
            // Get all active send currencies
            $sendCurrencies = Country::select('id', 'name', 'code', 'minimum_amount', 'maximum_amount', 'rate', 'image')
                ->where('send_from', 1)
                ->where('status', 1)
                ->orderBy('name')
                ->get()
                ->map(function ($currency) {
                    return [
                        'id' => $currency->id,
                        'name' => $currency->name,
                        'code' => $currency->code,
                        'minimum_amount' => (float) $currency->minimum_amount,
                        'maximum_amount' => (float) $currency->maximum_amount,
                        'rate' => (float) $currency->rate,
                        'flag' => getFile(config('location.country.path') . $currency->image)
                    ];
                });

            return response()->json([
                'status' => true,
                'message' => 'Send currencies retrieved successfully',
                'data' => [
                    'currencies' => $sendCurrencies,
                    'total_count' => $sendCurrencies->count()
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve send currencies'
            ], 500);
        }
    }

    /**
     * Get available receive currencies for remittance
     * Returns all active currencies that can be received in remittance transactions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableReceiveCurrencies(Request $request)
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        try {
            $query = Country::select('id', 'name', 'code', 'minimum_amount', 'maximum_amount', 'rate', 'image')
                ->where('send_to', 1)
                ->where('status', 1);

            // Optional: Filter by merchant's assigned country if they have country restrictions
            if ($user->country_id && $request->has('merchant_country_only') && $request->merchant_country_only == 1) {
                $query->where('id', $user->country_id);
            }

            $receiveCurrencies = $query->orderBy('name')
                ->get()
                ->map(function ($currency) {
                    return [
                        'id' => $currency->id,
                        'name' => $currency->name,
                        'code' => $currency->code,
                        'minimum_amount' => (float) $currency->minimum_amount,
                        'maximum_amount' => (float) $currency->maximum_amount,
                        'rate' => (float) $currency->rate,
                        'flag' => getFile(config('location.country.path') . $currency->image)
                    ];
                });

            return response()->json([
                'status' => true,
                'message' => 'Receive currencies retrieved successfully',
                'data' => [
                    'currencies' => $receiveCurrencies,
                    'total_count' => $receiveCurrencies->count(),
                    'merchant_country_id' => $user->country_id
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve receive currencies'
            ], 500);
        }
    }

    /**
     * Calculate remittance fees for a transaction
     * Returns service charges, merchant commissions, exchange rates, total fees, and net amount
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateRemittanceFees(Request $request)
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        // Validation
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'send_currency_id' => 'required|exists:countries,id',
            'receive_currency_id' => 'required|exists:countries,id',
            'service_id' => 'required|exists:services,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // Get currencies and service
            $sendCountry = Country::where('id', $request->send_currency_id)
                ->where('send_from', 1)
                ->where('status', 1)
                ->first();

            if (!$sendCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid send currency'
                ], 400);
            }

            $receiveCountry = Country::where('id', $request->receive_currency_id)
                ->where('send_to', 1)
                ->where('status', 1)
                ->first();

            if (!$receiveCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid receive currency'
                ], 400);
            }

            $service = Service::where('id', $request->service_id)
                ->where('status', 1)
                ->first();

            if (!$service) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid service'
                ], 400);
            }

            // Check amount limits
            $amount = $request->amount;
            if ($amount < $sendCountry->minimum_amount || $amount > $sendCountry->maximum_amount) {
                return response()->json([
                    'status' => false,
                    'message' => "Amount must be between {$sendCountry->minimum_amount} and {$sendCountry->maximum_amount} {$sendCountry->code}"
                ], 400);
            }

            // Calculate exchange rate and fees using existing logic
            $rate = $receiveCountry->rate / $sendCountry->rate;
            $fees = getCharge($amount, $receiveCountry->id, $service->id);
            $totalPayable = $amount + $fees;
            $recipientGetAmount = $amount * $rate;

            // Round up (ceiling) the final amount as per requirements
            $totalPayable = ceil($totalPayable);

            // Calculate merchant commission
            $merchantCom = 0;
            if ($fees > 0) {
                $basicCom = config('basic.merchant_commission', 0); // percent
                $baseCharge = $fees / $sendCountry->rate;
                $merchantCom = ($baseCharge * $basicCom) / 100;
            }

            return response()->json([
                'status' => true,
                'message' => 'Fees calculated successfully',
                'data' => [
                    'send_amount' => (float) $amount,
                    'send_currency' => $sendCountry->code,
                    'receive_currency' => $receiveCountry->code,
                    'exchange_rate' => round($rate, config('basic.fraction_number', 2)),
                    'service_fees' => round($fees, 2),
                    'total_payable' => (float) $totalPayable,
                    'recipient_gets' => round($recipientGetAmount, 2),
                    'merchant_commission' => round($merchantCom, 4),
                    'service_name' => $service->name,
                    'calculation_details' => [
                        'base_amount' => (float) $amount,
                        'service_charge' => round($fees, 2),
                        'total_with_fees' => round($amount + $fees, 2),
                        'total_payable_rounded' => (float) $totalPayable,
                        'exchange_calculation' => [
                            'send_rate' => (float) $sendCountry->rate,
                            'receive_rate' => (float) $receiveCountry->rate,
                            'calculated_rate' => round($rate, 6)
                        ]
                    ]
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to calculate fees'
            ], 500);
        }
    }
}

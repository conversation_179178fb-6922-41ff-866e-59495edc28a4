<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payout extends Model
{
	use HasFactory;

	protected $guarded = 'id';
	protected $casts = [
		'meta_field' => 'object',
        'withdraw_information' => 'object',
	];

	/**
	 * Boot the model and add event listeners
	 */
	protected static function boot()
	{
		parent::boot();

		// Prevent deletion of payouts with associated transactions
		static::deleting(function ($payout) {
			// Check if there are associated transactions
			if ($payout->transactions()->exists()) {
				throw new \Exception('Cannot delete payout record with associated transactions. Payout ID: ' . $payout->id);
			}
		});
	}

	/**
	 * Get all transactions associated with this payout.
	 */
	public function transactions()
	{
		return $this->hasMany(Transaction::class, 'payout_id', 'id');
	}

	/**
	 * Get the debit transaction for this payout.
	 */
	public function debitTransaction()
	{
		return $this->hasOne(Transaction::class, 'payout_id', 'id')
					->where('transaction_type', Transaction::TYPE_WITHDRAWAL_DEBIT);
	}

	public function transactional()
	{
		return $this->morphOne(Transaction::class, 'transactional');
	}

	public function user()
	{
		return $this->belongsTo(User::class, 'user_id', 'id');
	}

	public function admin()
	{
		return $this->belongsTo(Admin::class, 'admin_id', 'id');
	}

	public function payoutMethod()
	{
		return $this->belongsTo(PayoutMethod::class, 'payout_method_id', 'id');
	}
	
	public function method()
	{
		return $this->belongsTo(PayoutMethod::class, 'payout_method_id', 'id');
	}
}

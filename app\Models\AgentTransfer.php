<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentTransfer extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    /**
     * Boot the model and add event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Prevent deletion of agent transfers with associated transactions
        static::deleting(function ($agentTransfer) {
            // Check if there are associated transactions
            if ($agentTransfer->transactions()->exists()) {
                throw new \Exception('Cannot delete agent transfer record with associated transactions. Agent Transfer ID: ' . $agentTransfer->id);
            }
        });
    }

    /**
     * Get the sender of the transfer.
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the receiver of the transfer.
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * Get all transactions associated with this agent transfer.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'agent_transfer_id', 'id');
    }

    /**
     * Get the debit transaction for this agent transfer (sender).
     */
    public function debitTransaction()
    {
        return $this->hasOne(Transaction::class, 'agent_transfer_id', 'id')
                    ->where('transaction_type', Transaction::TYPE_AGENT_TRANSFER_DEBIT);
    }

    /**
     * Get the credit transaction for this agent transfer (receiver).
     */
    public function creditTransaction()
    {
        return $this->hasOne(Transaction::class, 'agent_transfer_id', 'id')
                    ->where('transaction_type', Transaction::TYPE_AGENT_TRANSFER_CREDIT);
    }

    /**
     * Get the transaction associated with this transfer (legacy morphOne).
     */
    public function transaction()
    {
        return $this->morphOne(Transaction::class, 'transactional');
    }
}

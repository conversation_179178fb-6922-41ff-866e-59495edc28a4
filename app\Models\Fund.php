<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Fund extends Model
{
    protected $guarded = ['id'];
    protected $table = "funds";

    protected $casts = [
        'detail' => 'object'
    ];

    /**
     * Boot the model and add event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Prevent deletion of funds with associated transactions
        static::deleting(function ($fund) {
            // Check if there are associated transactions
            if ($fund->transactions()->exists()) {
                throw new \Exception('Cannot delete fund record with associated transactions. Fund ID: ' . $fund->id);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
    public function gateway()
    {
        return $this->belongsTo(Gateway::class, 'gateway_id');
    }
    /**
     * Get all transactions associated with this fund.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'fund_id', 'id');
    }

    /**
     * Get the deposit transaction for this fund.
     */
    public function depositTransaction()
    {
        return $this->hasOne(Transaction::class, 'fund_id', 'id')
                    ->where('transaction_type', Transaction::TYPE_FUND_DEPOSIT);
    }

    /**
     * Get the approval transaction for this fund.
     */
    public function approvalTransaction()
    {
        return $this->hasOne(Transaction::class, 'fund_id', 'id')
                    ->where('transaction_type', Transaction::TYPE_FUND_DEPOSIT_APPROVAL);
    }

    public function sendmoney()
    {
        return $this->belongsTo(SendMoney::class, 'send_money_id');
    }

}

# Laravel & Composer
/vendor
/vendor/*
/composer.lock

# NPM/Node dependencies
/node_modules

# Generated files
/public/storage
/storage/*.key
/storage/app/public
/storage/framework/cache/data
/storage/framework/sessions
/storage/framework/views
/storage/logs

# Environment files (do NOT commit your secrets!)
.env
.env.*.backup

# IDEs and Editors
/.idea
/.vscode
/*.sublime-project
/*.sublime-workspace

# OS Files
.DS_Store
Thumbs.db

# Logs and caches
*.log
.phpunit.result.cache

# Laravel Mix (if using)
/public/hot
/public/mix-manifest.json

# Homestead and Vagrant
Homestead.json
Homestead.yaml
/.vagrant

# Temporary files
npm-debug.log*
yarn-error.log*



/assets/uploads/content/
/assets/uploads/send_money/
/.vs


composer.json
composer.lock
config/basic.php
config/requirements.php

assets/uploads

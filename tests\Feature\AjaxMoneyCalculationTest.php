<?php

namespace Tests\Feature;

use App\Models\Country;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AjaxMoneyCalculationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $sendCountry;
    protected $receiveCountry;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test countries with specific min/max amounts
        $this->sendCountry = Country::factory()->create([
            'name' => 'United States',
            'code' => 'USD',
            'send_from' => 1,
            'send_to' => 0,
            'status' => 1,
            'rate' => 1.0,
            'minimum_amount' => 10,
            'maximum_amount' => 1000,
            'facilities' => json_encode([1])
        ]);

        $this->receiveCountry = Country::factory()->create([
            'name' => 'Nigeria',
            'code' => 'NGN',
            'send_from' => 0,
            'send_to' => 1,
            'status' => 1,
            'rate' => 750.0,
            'minimum_amount' => 1000,
            'maximum_amount' => 500000,
            'facilities' => json_encode([
                (object)['id' => 1, 'name' => 'Bank Transfer']
            ])
        ]);

        // Create a test service
        $this->service = Service::factory()->create([
            'name' => 'Bank Transfer',
            'status' => 1,
        ]);
    }

    /** @test */
    public function it_validates_send_amount_within_limits_when_send_option_selected()
    {
        $requestData = [
            'amount' => 50, // Valid amount within limits (10-1000)
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'send',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'sendCountry',
                    'receiveCountry',
                    'rate',
                    'send_currency',
                    'receive_currency',
                    'send_amount',
                    'fees',
                    'total_payable',
                    'recipient_get'
                ]);
    }

    /** @test */
    public function it_rejects_send_amount_below_minimum_when_send_option_selected()
    {
        $requestData = [
            'amount' => 5, // Below minimum (10)
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'send',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure(['errors'])
                ->assertJsonPath('errors.amount.0', 'Minimum amount 10 USD');
    }

    /** @test */
    public function it_rejects_send_amount_above_maximum_when_send_option_selected()
    {
        $requestData = [
            'amount' => 1500, // Above maximum (1000)
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'send',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure(['errors'])
                ->assertJsonPath('errors.amount.0', 'Maximum amount 1,000 USD');
    }

    /** @test */
    public function it_validates_calculated_send_amount_when_receive_option_selected()
    {
        // Receive amount that results in valid send amount
        // Rate: NGN 750 = USD 1, so NGN 37500 = USD 50 (within limits 10-1000)
        $requestData = [
            'amount' => 37500, // Receive amount in NGN
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'receive',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'sendCountry',
                    'receiveCountry',
                    'rate',
                    'send_currency',
                    'receive_currency',
                    'send_amount',
                    'fees',
                    'total_payable',
                    'recipient_get'
                ]);
    }

    /** @test */
    public function it_rejects_receive_amount_that_results_in_send_amount_below_minimum()
    {
        // Receive amount that results in send amount below minimum
        // Rate: NGN 750 = USD 1, so NGN 3750 = USD 5 (below minimum 10)
        $requestData = [
            'amount' => 3750, // Receive amount in NGN
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'receive',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure(['errors'])
                ->assertJson([
                    'errors' => [
                        'amount' => ['Minimum send amount 10 USD (calculated from receive amount)']
                    ]
                ]);
    }

    /** @test */
    public function it_rejects_receive_amount_that_results_in_send_amount_above_maximum()
    {
        // Receive amount that results in send amount above maximum
        // Rate: NGN 750 = USD 1, so NGN 1125000 = USD 1500 (above maximum 1000)
        $requestData = [
            'amount' => 1125000, // Receive amount in NGN
            'sendCountry' => $this->sendCountry->id,
            'getCountry' => $this->receiveCountry->id,
            'serviceId' => 1,
            'sendReceive' => 'receive',
            '_token' => csrf_token()
        ];

        $response = $this->postJson(route('ajaxMoneyCalculation'), $requestData);

        $response->assertStatus(200)
                ->assertJsonStructure(['errors'])
                ->assertJson([
                    'errors' => [
                        'amount' => ['Maximum send amount 1,000 USD (calculated from receive amount)']
                    ]
                ]);
    }
}

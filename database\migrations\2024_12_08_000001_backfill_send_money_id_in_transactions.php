<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class BackfillSendMoneyIdInTransactions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // This migration backfills the send_money_id and transaction_type fields
        // for existing transactions by parsing the remarks field
        
        $this->backfillSendMoneyTransactions();
        $this->backfillCommissionTransactions();
        $this->backfillAgentTransferTransactions();
    }

    /**
     * Backfill send money debit transactions
     */
    private function backfillSendMoneyTransactions()
    {
        // Find transactions with remarks like "Send money Invoice: XXXXX"
        $transactions = DB::table('transactions')
            ->whereNull('send_money_id')
            ->where('remarks', 'LIKE', 'Send money Invoice:%')
            ->where('trx_type', '-')
            ->get();

        foreach ($transactions as $transaction) {
            // Extract invoice number from remarks
            if (preg_match('/Send money Invoice:\s*(\d+)/', $transaction->remarks, $matches)) {
                $invoice = $matches[1];
                
                // Find corresponding send_money record
                $sendMoney = DB::table('send_money')
                    ->where('invoice', $invoice)
                    ->first();
                
                if ($sendMoney) {
                    DB::table('transactions')
                        ->where('id', $transaction->id)
                        ->update([
                            'send_money_id' => $sendMoney->id,
                            'transaction_type' => 'debit'
                        ]);
                    
                    echo "Updated transaction {$transaction->id} with send_money_id {$sendMoney->id} (debit)\n";
                }
            }
        }
    }

    /**
     * Backfill commission transactions
     */
    private function backfillCommissionTransactions()
    {
        // Find transactions with remarks like "You got commission from #XXXXX"
        $transactions = DB::table('transactions')
            ->whereNull('send_money_id')
            ->where('remarks', 'LIKE', 'You got commission from #%')
            ->where('trx_type', '+')
            ->get();

        foreach ($transactions as $transaction) {
            // Extract invoice number from remarks
            if (preg_match('/You got commission from #(\d+)/', $transaction->remarks, $matches)) {
                $invoice = $matches[1];
                
                // Find corresponding send_money record
                $sendMoney = DB::table('send_money')
                    ->where('invoice', $invoice)
                    ->first();
                
                if ($sendMoney) {
                    DB::table('transactions')
                        ->where('id', $transaction->id)
                        ->update([
                            'send_money_id' => $sendMoney->id,
                            'transaction_type' => 'commission'
                        ]);
                    
                    echo "Updated transaction {$transaction->id} with send_money_id {$sendMoney->id} (commission)\n";
                }
            }
        }
    }

    /**
     * Backfill agent transfer transactions (these don't need send_money_id)
     */
    private function backfillAgentTransferTransactions()
    {
        // Find agent transfer transactions and mark them appropriately
        $transactions = DB::table('transactions')
            ->whereNull('transaction_type')
            ->where(function($query) {
                $query->where('remarks', 'LIKE', 'Transfer to agent @%')
                      ->orWhere('remarks', 'LIKE', 'Received from agent @%');
            })
            ->get();

        foreach ($transactions as $transaction) {
            $transactionType = null;
            
            if (strpos($transaction->remarks, 'Transfer to agent') !== false) {
                $transactionType = 'agent_transfer_debit';
            } elseif (strpos($transaction->remarks, 'Received from agent') !== false) {
                $transactionType = 'agent_transfer_credit';
            }
            
            if ($transactionType) {
                DB::table('transactions')
                    ->where('id', $transaction->id)
                    ->update(['transaction_type' => $transactionType]);
                
                echo "Updated transaction {$transaction->id} with transaction_type {$transactionType}\n";
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Reset the backfilled data
        DB::table('transactions')
            ->whereNotNull('send_money_id')
            ->update([
                'send_money_id' => null,
                'transaction_type' => null
            ]);
    }
}

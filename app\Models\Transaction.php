<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    // Transaction type constants for idempotent operations
    const TYPE_DEBIT = 'debit';
    const TYPE_CREDIT = 'credit';
    const TYPE_COMMISSION = 'commission';

    protected $guarded = ['id'];

    protected $fillable = [
        'user_id',
        'send_money_id',
        'transaction_type',
        'amount',
        'charge',
        'trx_type',
        'final_balance',
        'trx_id',
        'remarks'
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the send money record associated with this transaction.
     */
    public function sendMoney()
    {
        return $this->belongsTo(SendMoney::class, 'send_money_id', 'id');
    }

    /**
     * Get all available transaction types.
     */
    public static function getTransactionTypes()
    {
        return [
            self::TYPE_DEBIT,
            self::TYPE_CREDIT,
            self::TYPE_COMMISSION,
        ];
    }

    /**
     * Scope to filter transactions by send money ID.
     */
    public function scopeBySendMoney($query, $sendMoneyId)
    {
        return $query->where('send_money_id', $sendMoneyId);
    }

    /**
     * Scope to filter transactions by transaction type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }
}

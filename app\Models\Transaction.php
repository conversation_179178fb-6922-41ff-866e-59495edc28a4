<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    // Send Money Related Transaction Types (require send_money_id for idempotency)
    const TYPE_SEND_MONEY_DEBIT = 'send_money_debit';
    const TYPE_SEND_MONEY_COMMISSION = 'send_money_commission';
    const TYPE_SEND_MONEY_PAYOUT = 'send_money_payout';
    const TYPE_SEND_MONEY_PROFIT = 'send_money_profit';

    // Fund Management Transaction Types (no send_money_id required)
    const TYPE_FUND_DEPOSIT = 'fund_deposit';
    const TYPE_FUND_DEPOSIT_APPROVAL = 'fund_deposit_approval';
    const TYPE_FUND_REFUND = 'fund_refund';

    // Withdrawal/Payout Transaction Types (no send_money_id required)
    const TYPE_WITHDRAWAL_DEBIT = 'withdrawal_debit';
    const TYPE_PAYOUT_DEBIT = 'payout_debit';

    // Agent Transfer Transaction Types (no send_money_id required)
    const TYPE_AGENT_TRANSFER_DEBIT = 'agent_transfer_debit';
    const TYPE_AGENT_TRANSFER_CREDIT = 'agent_transfer_credit';

    // System Transaction Types (no send_money_id required)
    const TYPE_SYSTEM_ADJUSTMENT = 'system_adjustment';
    const TYPE_FEE_COLLECTION = 'fee_collection';
    const TYPE_BONUS_CREDIT = 'bonus_credit';

    // Legacy constants for backward compatibility
    const TYPE_DEBIT = 'send_money_debit';
    const TYPE_CREDIT = 'send_money_payout';
    const TYPE_COMMISSION = 'send_money_commission';

    protected $guarded = ['id'];

    protected $fillable = [
        'user_id',
        'send_money_id',
        'transaction_type',
        'amount',
        'charge',
        'trx_type',
        'final_balance',
        'trx_id',
        'remarks'
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the send money record associated with this transaction.
     */
    public function sendMoney()
    {
        return $this->belongsTo(SendMoney::class, 'send_money_id', 'id');
    }

    /**
     * Get the payout record associated with this transaction.
     */
    public function payout()
    {
        return $this->belongsTo(Payout::class, 'payout_id', 'id');
    }

    /**
     * Get the agent transfer record associated with this transaction.
     */
    public function agentTransfer()
    {
        return $this->belongsTo(AgentTransfer::class, 'agent_transfer_id', 'id');
    }

    /**
     * Get the fund record associated with this transaction.
     */
    public function fund()
    {
        return $this->belongsTo(Fund::class, 'fund_id', 'id');
    }

    /**
     * Get all send money related transaction types (require send_money_id).
     */
    public static function getSendMoneyTransactionTypes()
    {
        return [
            self::TYPE_SEND_MONEY_DEBIT,
            self::TYPE_SEND_MONEY_COMMISSION,
            self::TYPE_SEND_MONEY_PAYOUT,
            self::TYPE_SEND_MONEY_PROFIT,
        ];
    }

    /**
     * Get all non-send money transaction types (no send_money_id required).
     */
    public static function getGeneralTransactionTypes()
    {
        return [
            self::TYPE_FUND_DEPOSIT,
            self::TYPE_FUND_DEPOSIT_APPROVAL,
            self::TYPE_FUND_REFUND,
            self::TYPE_WITHDRAWAL_DEBIT,
            self::TYPE_PAYOUT_DEBIT,
            self::TYPE_AGENT_TRANSFER_DEBIT,
            self::TYPE_AGENT_TRANSFER_CREDIT,
            self::TYPE_SYSTEM_ADJUSTMENT,
            self::TYPE_FEE_COLLECTION,
            self::TYPE_BONUS_CREDIT,
        ];
    }

    /**
     * Get all available transaction types.
     */
    public static function getAllTransactionTypes()
    {
        return array_merge(
            self::getSendMoneyTransactionTypes(),
            self::getGeneralTransactionTypes()
        );
    }

    /**
     * Check if a transaction type requires send_money_id.
     */
    public static function requiresSendMoneyId($transactionType)
    {
        return in_array($transactionType, self::getSendMoneyTransactionTypes());
    }

    /**
     * Check if a transaction type requires payout_id.
     */
    public static function requiresPayoutId($transactionType)
    {
        return in_array($transactionType, [
            self::TYPE_WITHDRAWAL_DEBIT,
            self::TYPE_PAYOUT_DEBIT,
        ]);
    }

    /**
     * Check if a transaction type requires agent_transfer_id.
     */
    public static function requiresAgentTransferId($transactionType)
    {
        return in_array($transactionType, [
            self::TYPE_AGENT_TRANSFER_DEBIT,
            self::TYPE_AGENT_TRANSFER_CREDIT,
        ]);
    }

    /**
     * Check if a transaction type requires fund_id.
     */
    public static function requiresFundId($transactionType)
    {
        return in_array($transactionType, [
            self::TYPE_FUND_DEPOSIT,
            self::TYPE_FUND_DEPOSIT_APPROVAL,
            self::TYPE_FUND_REFUND,
        ]);
    }

    /**
     * Get the reference ID for this transaction based on its type.
     */
    public function getReferenceId()
    {
        if ($this->send_money_id) {
            return $this->send_money_id;
        } elseif ($this->payout_id) {
            return $this->payout_id;
        } elseif ($this->agent_transfer_id) {
            return $this->agent_transfer_id;
        } elseif ($this->fund_id) {
            return $this->fund_id;
        }
        return null;
    }

    /**
     * Get the reference type for this transaction.
     */
    public function getReferenceType()
    {
        if ($this->send_money_id) {
            return 'send_money';
        } elseif ($this->payout_id) {
            return 'payout';
        } elseif ($this->agent_transfer_id) {
            return 'agent_transfer';
        } elseif ($this->fund_id) {
            return 'fund';
        }
        return null;
    }

    /**
     * Scope to filter transactions by send money operations.
     */
    public function scopeForSendMoney($query, $sendMoneyId = null)
    {
        if ($sendMoneyId) {
            return $query->where('send_money_id', $sendMoneyId);
        }
        return $query->whereNotNull('send_money_id');
    }

    /**
     * Scope to filter transactions by payout operations.
     */
    public function scopeForPayout($query, $payoutId = null)
    {
        if ($payoutId) {
            return $query->where('payout_id', $payoutId);
        }
        return $query->whereNotNull('payout_id');
    }

    /**
     * Scope to filter transactions by agent transfer operations.
     */
    public function scopeForAgentTransfer($query, $agentTransferId = null)
    {
        if ($agentTransferId) {
            return $query->where('agent_transfer_id', $agentTransferId);
        }
        return $query->whereNotNull('agent_transfer_id');
    }

    /**
     * Scope to filter transactions by fund operations.
     */
    public function scopeForFund($query, $fundId = null)
    {
        if ($fundId) {
            return $query->where('fund_id', $fundId);
        }
        return $query->whereNotNull('fund_id');
    }

    /**
     * Scope to filter transactions by transaction type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    /**
     * Scope to filter transactions that have any reference ID (idempotent transactions).
     */
    public function scopeWithReference($query)
    {
        return $query->where(function ($q) {
            $q->whereNotNull('send_money_id')
              ->orWhereNotNull('payout_id')
              ->orWhereNotNull('agent_transfer_id')
              ->orWhereNotNull('fund_id');
        });
    }

    /**
     * Scope to filter transactions without any reference ID (legacy transactions).
     */
    public function scopeWithoutReference($query)
    {
        return $query->whereNull('send_money_id')
                    ->whereNull('payout_id')
                    ->whereNull('agent_transfer_id')
                    ->whereNull('fund_id');
    }
}

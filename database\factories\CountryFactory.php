<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

class CountryFactory extends Factory
{
    protected $model = Country::class;

    public function definition()
    {
        return [
            'name' => $this->faker->country,
            'code' => $this->faker->currencyCode,
            'iso_code' => $this->faker->countryCode,
            'continent_id' => 1,
            'rate' => $this->faker->randomFloat(2, 0.5, 1000),
            'minimum_amount' => $this->faker->numberBetween(1, 100),
            'maximum_amount' => $this->faker->numberBetween(1000, 100000),
            'status' => 1,
            'send_from' => $this->faker->boolean,
            'send_to' => $this->faker->boolean,
            'facilities' => json_encode([]),
            'image' => 'default.png',
            'details' => $this->faker->text(200),
        ];
    }

    public function sendFrom()
    {
        return $this->state(function (array $attributes) {
            return [
                'send_from' => 1,
                'send_to' => 0,
            ];
        });
    }

    public function sendTo()
    {
        return $this->state(function (array $attributes) {
            return [
                'send_from' => 0,
                'send_to' => 1,
            ];
        });
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
            ];
        });
    }
}

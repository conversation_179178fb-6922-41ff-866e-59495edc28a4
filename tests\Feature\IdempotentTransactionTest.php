<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SendMoney;
use App\Models\Transaction;
use App\Services\BasicService;
use App\Exceptions\DuplicateTransactionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;

class IdempotentTransactionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $sendMoney;
    protected $basicService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'balance' => 1000.00,
            'merchant' => 1
        ]);

        // Create test send money record
        $this->sendMoney = SendMoney::create([
            'user_id' => $this->user->id,
            'invoice' => 123456,
            'send_amount' => 100.00,
            'fees' => 5.00,
            'payable_amount' => 105.00,
            'payment_status' => 1,
            'status' => 1
        ]);

        $this->basicService = new BasicService();
    }

    /** @test */
    public function it_creates_transaction_successfully_on_first_attempt()
    {
        $transaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Test transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        $this->assertInstanceOf(Transaction::class, $transaction);
        $this->assertEquals($this->sendMoney->id, $transaction->send_money_id);
        $this->assertEquals(Transaction::TYPE_DEBIT, $transaction->transaction_type);
        $this->assertEquals($this->user->id, $transaction->user_id);
    }

    /** @test */
    public function it_prevents_duplicate_transactions_with_same_send_money_id_and_type()
    {
        // Create first transaction
        $firstTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Test transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        $this->assertInstanceOf(Transaction::class, $firstTransaction);

        // Attempt to create duplicate transaction
        $secondTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX124',
            'Duplicate transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Should return the existing transaction
        $this->assertInstanceOf(Transaction::class, $secondTransaction);
        $this->assertEquals($firstTransaction->id, $secondTransaction->id);

        // Verify only one transaction exists in database
        $count = Transaction::where('send_money_id', $this->sendMoney->id)
                           ->where('transaction_type', Transaction::TYPE_DEBIT)
                           ->count();
        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_allows_different_transaction_types_for_same_send_money()
    {
        // Create debit transaction
        $debitTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Debit transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Create commission transaction (different type)
        $commissionTransaction = $this->basicService->makeTransaction(
            $this->user,
            10.00,
            0.00,
            '+',
            'TRX124',
            'Commission transaction',
            $this->sendMoney->id,
            Transaction::TYPE_COMMISSION
        );

        $this->assertInstanceOf(Transaction::class, $debitTransaction);
        $this->assertInstanceOf(Transaction::class, $commissionTransaction);
        $this->assertNotEquals($debitTransaction->id, $commissionTransaction->id);

        // Verify both transactions exist
        $debitCount = Transaction::where('send_money_id', $this->sendMoney->id)
                                ->where('transaction_type', Transaction::TYPE_DEBIT)
                                ->count();
        $commissionCount = Transaction::where('send_money_id', $this->sendMoney->id)
                                     ->where('transaction_type', Transaction::TYPE_COMMISSION)
                                     ->count();

        $this->assertEquals(1, $debitCount);
        $this->assertEquals(1, $commissionCount);
    }

    /** @test */
    public function it_handles_concurrent_transaction_attempts()
    {
        // Simulate concurrent requests by attempting to create the same transaction multiple times
        $results = [];
        
        for ($i = 0; $i < 5; $i++) {
            try {
                $transaction = $this->basicService->makeTransaction(
                    $this->user,
                    100.00,
                    5.00,
                    '-',
                    'TRX' . $i,
                    'Concurrent transaction ' . $i,
                    $this->sendMoney->id,
                    Transaction::TYPE_DEBIT
                );
                $results[] = $transaction;
            } catch (DuplicateTransactionException $e) {
                // This is expected for duplicate attempts
                $results[] = null;
            }
        }

        // Filter out null results
        $validTransactions = array_filter($results);
        
        // Should have at least one successful transaction
        $this->assertGreaterThanOrEqual(1, count($validTransactions));

        // Verify only one transaction exists in database
        $count = Transaction::where('send_money_id', $this->sendMoney->id)
                           ->where('transaction_type', Transaction::TYPE_DEBIT)
                           ->count();
        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_maintains_data_integrity_with_database_constraints()
    {
        // Create first transaction
        $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'First transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Attempt to insert duplicate directly via database (bypassing application logic)
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        DB::table('transactions')->insert([
            'user_id' => $this->user->id,
            'send_money_id' => $this->sendMoney->id,
            'transaction_type' => Transaction::TYPE_DEBIT,
            'amount' => 100.00,
            'charge' => 5.00,
            'trx_type' => '-',
            'trx_id' => 'TRX124',
            'remarks' => 'Direct database insert',
            'final_balance' => $this->user->balance,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /** @test */
    public function it_works_with_legacy_transactions_without_send_money_id()
    {
        // Create legacy transaction (without send_money_id)
        $legacyTransaction = $this->basicService->makeTransaction(
            $this->user,
            50.00,
            2.50,
            '-',
            'LEGACY123',
            'Legacy transaction'
        );

        $this->assertInstanceOf(Transaction::class, $legacyTransaction);
        $this->assertNull($legacyTransaction->send_money_id);
        $this->assertNull($legacyTransaction->transaction_type);
    }

    /** @test */
    public function it_handles_send_money_relationships_correctly()
    {
        // Create transactions for the send money record
        $debitTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Debit transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        $commissionTransaction = $this->basicService->makeTransaction(
            $this->user,
            10.00,
            0.00,
            '+',
            'TRX124',
            'Commission transaction',
            $this->sendMoney->id,
            Transaction::TYPE_COMMISSION
        );

        // Test relationships
        $this->assertEquals($this->sendMoney->id, $debitTransaction->sendMoney->id);
        $this->assertEquals($this->sendMoney->id, $commissionTransaction->sendMoney->id);

        // Test reverse relationships
        $transactions = $this->sendMoney->transactions;
        $this->assertEquals(2, $transactions->count());

        $debitTx = $this->sendMoney->debitTransaction;
        $commissionTx = $this->sendMoney->commissionTransaction;

        $this->assertNotNull($debitTx);
        $this->assertNotNull($commissionTx);
        $this->assertEquals(Transaction::TYPE_DEBIT, $debitTx->transaction_type);
        $this->assertEquals(Transaction::TYPE_COMMISSION, $commissionTx->transaction_type);
    }
}

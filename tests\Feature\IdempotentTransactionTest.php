<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SendMoney;
use App\Models\Transaction;
use App\Services\BasicService;
use App\Exceptions\DuplicateTransactionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;

class IdempotentTransactionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $sendMoney;
    protected $basicService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'balance' => 1000.00,
            'merchant' => 1
        ]);

        // Create test send money record
        $this->sendMoney = SendMoney::create([
            'user_id' => $this->user->id,
            'invoice' => 123456,
            'send_amount' => 100.00,
            'fees' => 5.00,
            'payable_amount' => 105.00,
            'payment_status' => 1,
            'status' => 1
        ]);

        $this->basicService = new BasicService();
    }

    /** @test */
    public function it_creates_transaction_successfully_on_first_attempt()
    {
        $transaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Test transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        $this->assertInstanceOf(Transaction::class, $transaction);
        $this->assertEquals($this->sendMoney->id, $transaction->send_money_id);
        $this->assertEquals(Transaction::TYPE_DEBIT, $transaction->transaction_type);
        $this->assertEquals($this->user->id, $transaction->user_id);
    }

    /** @test */
    public function it_prevents_duplicate_transactions_with_same_send_money_id_and_type()
    {
        // Create first transaction
        $firstTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Test transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        $this->assertInstanceOf(Transaction::class, $firstTransaction);

        // Attempt to create duplicate transaction
        $secondTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX124',
            'Duplicate transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Should return the existing transaction
        $this->assertInstanceOf(Transaction::class, $secondTransaction);
        $this->assertEquals($firstTransaction->id, $secondTransaction->id);

        // Verify only one transaction exists in database
        $count = Transaction::where('send_money_id', $this->sendMoney->id)
                           ->where('transaction_type', Transaction::TYPE_DEBIT)
                           ->count();
        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_allows_different_transaction_types_for_same_send_money()
    {
        // Create debit transaction
        $debitTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Debit transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Create commission transaction (different type)
        $commissionTransaction = $this->basicService->makeTransaction(
            $this->user,
            10.00,
            0.00,
            '+',
            'TRX124',
            'Commission transaction',
            $this->sendMoney->id,
            Transaction::TYPE_COMMISSION
        );

        $this->assertInstanceOf(Transaction::class, $debitTransaction);
        $this->assertInstanceOf(Transaction::class, $commissionTransaction);
        $this->assertNotEquals($debitTransaction->id, $commissionTransaction->id);

        // Verify both transactions exist
        $debitCount = Transaction::where('send_money_id', $this->sendMoney->id)
                                ->where('transaction_type', Transaction::TYPE_DEBIT)
                                ->count();
        $commissionCount = Transaction::where('send_money_id', $this->sendMoney->id)
                                     ->where('transaction_type', Transaction::TYPE_COMMISSION)
                                     ->count();

        $this->assertEquals(1, $debitCount);
        $this->assertEquals(1, $commissionCount);
    }

    /** @test */
    public function it_handles_concurrent_transaction_attempts()
    {
        // Simulate concurrent requests by attempting to create the same transaction multiple times
        $results = [];
        
        for ($i = 0; $i < 5; $i++) {
            try {
                $transaction = $this->basicService->makeTransaction(
                    $this->user,
                    100.00,
                    5.00,
                    '-',
                    'TRX' . $i,
                    'Concurrent transaction ' . $i,
                    $this->sendMoney->id,
                    Transaction::TYPE_DEBIT
                );
                $results[] = $transaction;
            } catch (DuplicateTransactionException $e) {
                // This is expected for duplicate attempts
                $results[] = null;
            }
        }

        // Filter out null results
        $validTransactions = array_filter($results);
        
        // Should have at least one successful transaction
        $this->assertGreaterThanOrEqual(1, count($validTransactions));

        // Verify only one transaction exists in database
        $count = Transaction::where('send_money_id', $this->sendMoney->id)
                           ->where('transaction_type', Transaction::TYPE_DEBIT)
                           ->count();
        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_maintains_data_integrity_with_database_constraints()
    {
        // Create first transaction
        $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'First transaction',
            $this->sendMoney->id,
            Transaction::TYPE_DEBIT
        );

        // Attempt to insert duplicate directly via database (bypassing application logic)
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        DB::table('transactions')->insert([
            'user_id' => $this->user->id,
            'send_money_id' => $this->sendMoney->id,
            'transaction_type' => Transaction::TYPE_DEBIT,
            'amount' => 100.00,
            'charge' => 5.00,
            'trx_type' => '-',
            'trx_id' => 'TRX124',
            'remarks' => 'Direct database insert',
            'final_balance' => $this->user->balance,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /** @test */
    public function it_works_with_legacy_transactions_without_send_money_id()
    {
        // Create legacy transaction (without send_money_id)
        $legacyTransaction = $this->basicService->makeTransaction(
            $this->user,
            50.00,
            2.50,
            '-',
            'LEGACY123',
            'Legacy transaction'
        );

        $this->assertInstanceOf(Transaction::class, $legacyTransaction);
        $this->assertNull($legacyTransaction->send_money_id);
        $this->assertNull($legacyTransaction->transaction_type);
    }

    /** @test */
    public function it_handles_send_money_relationships_correctly()
    {
        // Create transactions for the send money record
        $debitTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Debit transaction',
            $this->sendMoney->id,
            Transaction::TYPE_SEND_MONEY_DEBIT
        );

        $commissionTransaction = $this->basicService->makeTransaction(
            $this->user,
            10.00,
            0.00,
            '+',
            'TRX124',
            'Commission transaction',
            $this->sendMoney->id,
            Transaction::TYPE_SEND_MONEY_COMMISSION
        );

        // Test relationships
        $this->assertEquals($this->sendMoney->id, $debitTransaction->sendMoney->id);
        $this->assertEquals($this->sendMoney->id, $commissionTransaction->sendMoney->id);

        // Test reverse relationships
        $transactions = $this->sendMoney->transactions;
        $this->assertEquals(2, $transactions->count());

        $debitTx = $this->sendMoney->debitTransaction;
        $commissionTx = $this->sendMoney->commissionTransaction;

        $this->assertNotNull($debitTx);
        $this->assertNotNull($commissionTx);
        $this->assertEquals(Transaction::TYPE_SEND_MONEY_DEBIT, $debitTx->transaction_type);
        $this->assertEquals(Transaction::TYPE_SEND_MONEY_COMMISSION, $commissionTx->transaction_type);
    }

    /** @test */
    public function it_prevents_duplicate_payout_transactions()
    {
        // Create a payout record
        $payout = new \App\Models\Payout();
        $payout->user_id = $this->user->id;
        $payout->amount = 100.00;
        $payout->charge = 5.00;
        $payout->trx_id = 'PAYOUT123';
        $payout->save();

        // Create first transaction
        $transaction1 = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Withdrawal transaction',
            null, // No send_money_id
            Transaction::TYPE_WITHDRAWAL_DEBIT,
            $payout->id // payout_id for idempotency
        );

        $this->assertNotNull($transaction1);
        $this->assertEquals($payout->id, $transaction1->payout_id);
        $this->assertEquals(Transaction::TYPE_WITHDRAWAL_DEBIT, $transaction1->transaction_type);

        // Attempt to create duplicate transaction should be handled gracefully
        try {
            $transaction2 = $this->basicService->makeTransaction(
                $this->user,
                100.00,
                5.00,
                '-',
                'TRX124',
                'Duplicate withdrawal transaction',
                null, // No send_money_id
                Transaction::TYPE_WITHDRAWAL_DEBIT,
                $payout->id // Same payout_id and transaction_type
            );

            // If no exception, should return existing or handle gracefully
            $this->assertTrue(true);
        } catch (\App\Exceptions\DuplicateTransactionException $e) {
            // Expected behavior - duplicate prevented
            $this->assertTrue(true);
        }

        // Verify only one transaction exists for this payout and type
        $transactionCount = Transaction::where('payout_id', $payout->id)
                                     ->where('transaction_type', Transaction::TYPE_WITHDRAWAL_DEBIT)
                                     ->count();
        $this->assertEquals(1, $transactionCount);
    }

    /** @test */
    public function it_prevents_duplicate_agent_transfer_transactions()
    {
        // Create receiver user
        $receiver = new \App\Models\User();
        $receiver->username = 'receiver123';
        $receiver->email = '<EMAIL>';
        $receiver->password = bcrypt('password');
        $receiver->balance = 0.00;
        $receiver->merchant = 1;
        $receiver->save();

        // Create an agent transfer record
        $agentTransfer = new \App\Models\AgentTransfer();
        $agentTransfer->sender_id = $this->user->id;
        $agentTransfer->receiver_id = $receiver->id;
        $agentTransfer->amount = 100.00;
        $agentTransfer->fee = 5.00;
        $agentTransfer->final_amount = 95.00;
        $agentTransfer->trx_id = 'AGENT123';
        $agentTransfer->save();

        // Create first debit transaction
        $debitTransaction1 = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '-',
            'TRX123',
            'Agent transfer debit',
            null, // No send_money_id
            Transaction::TYPE_AGENT_TRANSFER_DEBIT,
            null, // No payout_id
            $agentTransfer->id // agent_transfer_id for idempotency
        );

        $this->assertNotNull($debitTransaction1);
        $this->assertEquals($agentTransfer->id, $debitTransaction1->agent_transfer_id);

        // Attempt to create duplicate debit transaction should be handled gracefully
        try {
            $debitTransaction2 = $this->basicService->makeTransaction(
                $this->user,
                100.00,
                5.00,
                '-',
                'TRX124',
                'Duplicate agent transfer debit',
                null, // No send_money_id
                Transaction::TYPE_AGENT_TRANSFER_DEBIT,
                null, // No payout_id
                $agentTransfer->id // Same agent_transfer_id and transaction_type
            );

            $this->assertTrue(true);
        } catch (\App\Exceptions\DuplicateTransactionException $e) {
            $this->assertTrue(true);
        }

        // Create credit transaction (different transaction_type, should work)
        $creditTransaction = $this->basicService->makeTransaction(
            $receiver,
            95.00,
            0.00,
            '+',
            'TRX125',
            'Agent transfer credit',
            null, // No send_money_id
            Transaction::TYPE_AGENT_TRANSFER_CREDIT,
            null, // No payout_id
            $agentTransfer->id // Same agent_transfer_id but different transaction_type
        );

        $this->assertNotNull($creditTransaction);
        $this->assertEquals($agentTransfer->id, $creditTransaction->agent_transfer_id);
        $this->assertEquals(Transaction::TYPE_AGENT_TRANSFER_CREDIT, $creditTransaction->transaction_type);
    }

    /** @test */
    public function it_prevents_duplicate_fund_transactions()
    {
        // Create a fund record
        $fund = new \App\Models\Fund();
        $fund->user_id = $this->user->id;
        $fund->amount = 100.00;
        $fund->charge = 5.00;
        $fund->transaction = 'FUND123';
        $fund->save();

        // Create first deposit transaction
        $depositTransaction1 = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '+',
            'TRX123',
            'Fund deposit',
            null, // No send_money_id
            Transaction::TYPE_FUND_DEPOSIT,
            null, // No payout_id
            null, // No agent_transfer_id
            $fund->id // fund_id for idempotency
        );

        $this->assertNotNull($depositTransaction1);
        $this->assertEquals($fund->id, $depositTransaction1->fund_id);
        $this->assertEquals(Transaction::TYPE_FUND_DEPOSIT, $depositTransaction1->transaction_type);

        // Attempt to create duplicate deposit transaction should be handled gracefully
        try {
            $depositTransaction2 = $this->basicService->makeTransaction(
                $this->user,
                100.00,
                5.00,
                '+',
                'TRX124',
                'Duplicate fund deposit',
                null, // No send_money_id
                Transaction::TYPE_FUND_DEPOSIT,
                null, // No payout_id
                null, // No agent_transfer_id
                $fund->id // Same fund_id and transaction_type
            );

            $this->assertTrue(true);
        } catch (\App\Exceptions\DuplicateTransactionException $e) {
            $this->assertTrue(true);
        }

        // Create approval transaction (different transaction_type, should work)
        $approvalTransaction = $this->basicService->makeTransaction(
            $this->user,
            100.00,
            5.00,
            '+',
            'TRX125',
            'Fund deposit approval',
            null, // No send_money_id
            Transaction::TYPE_FUND_DEPOSIT_APPROVAL,
            null, // No payout_id
            null, // No agent_transfer_id
            $fund->id // Same fund_id but different transaction_type
        );

        $this->assertNotNull($approvalTransaction);
        $this->assertEquals($fund->id, $approvalTransaction->fund_id);
        $this->assertEquals(Transaction::TYPE_FUND_DEPOSIT_APPROVAL, $approvalTransaction->transaction_type);
    }
}

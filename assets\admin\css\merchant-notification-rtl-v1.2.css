/* Minimal CSS fix for notification bell visibility in merchant dashboard */

/* Specific fix for notification bell visibility */
#pushNotificationArea {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

#pushNotificationArea .nav-link {
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

#pushNotificationArea .svg-icon,
#pushNotificationArea [data-feather="bell"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 20px !important;
    height: 20px !important;
    stroke: currentColor !important;
    stroke-width: 2 !important;
    stroke-linecap: round !important;
    stroke-linejoin: round !important;
    fill: none !important;
}

/* Ensure notification badge is visible and properly positioned */
/*
#pushNotificationArea .badge {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    transform: translate(50%, -50%) !important;
    z-index: 10 !important;
    min-width: 18px !important;
    height: 18px !important;
    border-radius: 50% !important;
    font-size: 11px !important;
    line-height: 18px !important;
    text-align: center !important;
}
*/

/* Force notification bell to be visible in both LTR and RTL modes */
.navbar-nav .nav-item#pushNotificationArea {
    display: block !important;
}

/* Ensure feather icons are properly rendered */
[data-feather="bell"] {
    display: inline-block !important;
    width: 20px !important;
    height: 20px !important;
    stroke: currentColor !important;
    stroke-width: 2 !important;
    stroke-linecap: round !important;
    stroke-linejoin: round !important;
    fill: none !important;
}

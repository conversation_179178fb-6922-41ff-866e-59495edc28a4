<?php

namespace App\Exceptions;

use Exception;

class DuplicateTransactionException extends Exception
{
    protected $sendMoneyId;
    protected $transactionType;
    protected $userId;

    public function __construct($message = "Duplicate transaction detected", $sendMoneyId = null, $transactionType = null, $userId = null, $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        
        $this->sendMoneyId = $sendMoneyId;
        $this->transactionType = $transactionType;
        $this->userId = $userId;
    }

    /**
     * Get the send money ID associated with the duplicate transaction
     */
    public function getSendMoneyId()
    {
        return $this->sendMoneyId;
    }

    /**
     * Get the transaction type associated with the duplicate transaction
     */
    public function getTransactionType()
    {
        return $this->transactionType;
    }

    /**
     * Get the user ID associated with the duplicate transaction
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * Render the exception into an HTTP response for API requests
     */
    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => false,
                'message' => 'Transaction already processed',
                'error_code' => 'DUPLICATE_TRANSACTION',
                'data' => [
                    'send_money_id' => $this->sendMoneyId,
                    'transaction_type' => $this->transactionType
                ]
            ], 409); // 409 Conflict
        }

        // For web requests, redirect with success message since transaction was already processed
        return redirect()->back()->with('success', 'Transaction has been processed successfully.');
    }

    /**
     * Report the exception for logging purposes
     */
    public function report()
    {
        \Log::info('Duplicate transaction attempt detected', [
            'send_money_id' => $this->sendMoneyId,
            'transaction_type' => $this->transactionType,
            'user_id' => $this->userId,
            'message' => $this->getMessage()
        ]);
    }
}

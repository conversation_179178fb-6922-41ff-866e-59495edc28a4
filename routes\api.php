<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Route::group(['middleware' => ['json.response']], function () {

    Route::get('app/config', 'API\BasicApiController@appConfig');
    Route::get('flutterwave-data', 'API\BasicApiController@flutterwaveData');
    Route::get('language/{id?}', 'API\BasicApiController@language');
    Route::post('password/forgot-password', 'API\ForgotPasswordController@sendResetLinkResponse')->name('passwords.sent');
    Route::post('password/reset', 'API\ResetPasswordController@sendResetResponse')->name('passwords.reset');


    Route::post('/recovery-pass/get-email', 'API\AuthController@getEmailForRecoverPass');
    Route::post('/recovery-pass/get-code', 'API\AuthController@getCodeForRecoverPass');
    Route::post('/update-pass', 'API\AuthController@updatePass');


    Route::post('/ajaxCheckAccount', 'FrontendController@ajaxCheckAccount')->name('ajaxCheckAccount');
    Route::get('/checkBasicStatus', function () {
        return basicControl()->only('identity_verification', 'address_verification');
    })->name('checkBasicStatus');


    Route::group(['middleware' => ['auth:api'], 'namespace' => 'API'], function () {
        Route::post('/mail-code', 'AuthenticateUserController@sendMailCode')->name('user.mail-code');
        Route::post('/mail-verify', 'AuthenticateUserController@mailVerify')->name('user.mail-verify');
        Route::post('/sms-code', 'AuthenticateUserController@sendSmsCode')->name('user.sms-code');
        Route::post('/sms-verify', 'AuthenticateUserController@smsVerify')->name('user.sms-verify');


        Route::get('/user', 'AuthenticateUserController@user');
        Route::group(['middleware' => ['CheckStatusApi']], function () {

            Route::get('/address-verifyClue', 'AuthenticateUserController@addressVerifyClue')->name('address.verifyClue');
            Route::post('/address-verify', 'AuthenticateUserController@addressVerify')->name('address.verify');

            Route::get('/identity-verifyClue', 'AuthenticateUserController@identityVerifyClue')->name('identity.verifyClue');
            Route::post('/identity-verify', 'AuthenticateUserController@')->name('identity.verify');


            Route::get('verification', 'BasicApiController@getVerification');
            Route::post('identity/submit', 'BasicApiController@identitySubmit');
            Route::post('address/submit', 'BasicApiController@addressSubmit');


            Route::middleware('apiKYC')->group(function () {

                Route::get('2FA-security', 'TwoFASecurityController@twoFASecurity');
                Route::post('2FA-security/enable', 'TwoFASecurityController@twoFASecurityEnable');
                Route::post('2FA-security/disable', 'TwoFASecurityController@twoFASecurityDisable');

                Route::get('support-ticket/list', 'SupportTicketController@ticketList');
                Route::post('support-ticket/create', 'SupportTicketController@ticketCreate');
                Route::get('support-ticket/view/{id}', 'SupportTicketController@ticketView');
                Route::get('support-ticket/download/{id}', 'SupportTicketController@ticketDownlaod')->name('api.ticket.download');
                Route::post('support-ticket/reply', 'SupportTicketController@ticketReply');


                Route::post('/updateProfile', 'AuthenticateUserController@updateProfile');
                Route::post('/updateInformation', 'AuthenticateUserController@updateInformation');
                Route::post('/changePassword', 'AuthenticateUserController@changePassword');

                Route::get('currencyList', 'BasicApiController@currencyList')->name('currencyList');
                Route::get('/toCountry/{id}', 'BasicApiController@toCountry')->name('toCountry');
                Route::post('/countryService', 'BasicApiController@countryService')->name('countryService');
                Route::post('/moneyCalculation', 'BasicApiController@moneyCalculation')->name('moneyCalculation');
                Route::post('/calculationProceed', 'BasicApiController@calculationProceed')->name('calculationProceed');
                Route::get('/transfer-form/{invoice}', 'BasicApiController@transferForm')->name('transfer-form');
                Route::post('/transfer-formSubmit/{invoice}', 'BasicApiController@transferFormSubmit')->name('transfer-formSubmit');

                Route::get('transaction', 'AuthenticateUserController@transaction');
                Route::get('transaction/search', 'AuthenticateUserController@transactionSearch');

                Route::get('/payment-history', 'AuthenticateUserController@fundHistory');
                Route::get('/payment-history/search', 'AuthenticateUserController@fundHistorySearch');

                Route::get('/transfer-log', 'AuthenticateUserController@transferLog');
                Route::get('/transfer-invoice/{invoice}', 'AuthenticateUserController@transferInfo')->name('transferInfo');
                Route::delete('/transfer-log/delete', 'AuthenticateUserController@transferLogDelete');

                Route::get('/pay-now', 'PaymentController@paymentGateways');
                Route::post('manual/payment/submit', 'PaymentController@manualPaymentSubmit');
                Route::post('other/payment', 'PaymentController@showOtherPayment');
                Route::post('card/payment', 'PaymentController@cardPayment');
                Route::post('payment/done', 'PaymentController@paymentDone');

                Route::get('pusher/config', 'BasicApiController@pusherConfig');

                Route::post('verify-account', 'BasicApiController@verifyAccount');
            });
        });
    });

    Route::post('/login', 'API\AuthController@login')->name('login.api');
    Route::post('/signup', 'API\AuthRegisterController@register')->name('register.api');

});


Route::post('payout/{code}', 'Admin\PayoutRecordController@payout')->name('payout');

Route::group(['middleware' => ['auth:api', 'apiKYC']], function () {
    Route::post('/merchant/payout', 'API\MerchantPayoutController@payout')->name('api.merchant.payout');
    Route::get('/merchant/payout/info', 'API\MerchantPayoutController@payoutInfo')->name('api.merchant.payout.info');

    // Merchant Send Money API endpoints
    Route::post('/merchant/send-money', 'API\MerchantSendMoneyController@sendMoney')->name('api.merchant.send-money');
    Route::get('/merchant/send-money/options', 'API\MerchantSendMoneyController@getAvailableOptions')->name('api.merchant.send-money.options');
    Route::post('/merchant/send-money/calculate-fees', 'API\MerchantSendMoneyController@calculateFees')->name('api.merchant.send-money.calculate-fees');

    // Merchant Remittance API endpoints
    Route::get('/merchant/remittance/send-currencies', 'API\MerchantRemittanceController@getAvailableSendCurrencies')->name('api.merchant.remittance.send-currencies');
    Route::get('/merchant/remittance/receive-currencies', 'API\MerchantRemittanceController@getAvailableReceiveCurrencies')->name('api.merchant.remittance.receive-currencies');
    Route::post('/merchant/remittance/calculate-fees', 'API\MerchantRemittanceController@calculateRemittanceFees')->name('api.merchant.remittance.calculate-fees');
});
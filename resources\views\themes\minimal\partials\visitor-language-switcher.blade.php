{{-- Visitor Language Switcher - Only visible to non-authenticated users --}}
@guest
<div class="visitor-language-switcher">
    <div class="dropdown">
        <a class="nav-link dropdown-toggle visitor-lang-toggle" href="javascript:void(0)" 
           id="visitorLanguageDropdown" role="button" data-toggle="dropdown" 
           aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-globe mr-1"></i>
            @php
                $currentLangCode = session('trans', 'en');
                $currentLang = \App\Models\Language::where('short_name', $currentLangCode)->where('is_active', 1)->first();
                if (!$currentLang) {
                    $currentLang = \App\Models\Language::where('is_active', 1)->where('default_status', 1)->first();
                    if (!$currentLang) {
                        $currentLang = \App\Models\Language::where('is_active', 1)->first();
                    }
                }
            @endphp
            <span class="visitor-lang-name">{{ $currentLang ? $currentLang->name : 'English' }}</span>
            <i class="fas fa-chevron-down ml-1"></i>
        </a>
        <div class="dropdown-menu dropdown-menu-right visitor-lang-dropdown" aria-labelledby="visitorLanguageDropdown">
            <div class="dropdown-header">
                <span class="font-weight-bold">@lang('Select Language')</span>
            </div>
            <div class="dropdown-divider"></div>
            @php
                $languages = \App\Models\Language::where('is_active', 1)->get();
            @endphp
            @foreach($languages as $language)
                <a class="dropdown-item visitor-language-switch" href="javascript:void(0)"
                   data-code="{{ $language->short_name }}"
                   data-rtl="{{ $language->rtl }}"
                   data-name="{{ $language->name }}">
                    <i class="fas fa-language mr-2"></i>
                    {{ $language->name }}
                    @if($language->short_name == session('trans'))
                        <i class="fas fa-check text-success ml-auto"></i>
                    @endif
                </a>
            @endforeach
        </div>
    </div>
</div>

<style>
.visitor-language-switcher {
    margin-left: 15px;
}

.visitor-lang-toggle {
    color: #333;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.visitor-lang-toggle:hover {
    background-color: #f8f9fa;
    color: #007bff;
    text-decoration: none;
}

.visitor-lang-dropdown {
    min-width: 180px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.visitor-lang-dropdown .dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.visitor-lang-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.visitor-lang-dropdown .dropdown-item.disabled {
    pointer-events: none;
    opacity: 0.6;
}

.visitor-lang-name {
    margin: 0 4px;
}

/* RTL Support */
[dir="rtl"] .visitor-language-switcher {
    margin-left: 0;
    margin-right: 15px;
}

[dir="rtl"] .visitor-lang-toggle {
    flex-direction: row-reverse;
}

[dir="rtl"] .visitor-lang-dropdown .dropdown-item {
    flex-direction: row-reverse;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .visitor-language-switcher {
        margin-left: 10px;
    }
    
    .visitor-lang-toggle {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .visitor-lang-name {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Visitor Language Switcher functionality - Only for guests
    const visitorLanguageSwitches = document.querySelectorAll('.visitor-language-switch');

    visitorLanguageSwitches.forEach(function(switchElement) {
        switchElement.addEventListener('click', function(e) {
            e.preventDefault();

            const languageCode = this.getAttribute('data-code');
            const languageName = this.getAttribute('data-name');
            const isRtl = parseInt(this.getAttribute('data-rtl'));
            const originalHtml = this.innerHTML;

            // Disable all language switches during the process
            visitorLanguageSwitches.forEach(function(el) {
                el.classList.add('disabled');
                el.style.pointerEvents = 'none';
            });

            // Show loading indicator
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> @lang("Switching...")';

            // Make AJAX request to switch language
            fetch("{{ route('language') }}/" + languageCode, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
                timeout: 10000
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                // Apply RTL changes immediately if needed
                if (isRtl === 1) {
                    document.documentElement.setAttribute('dir', 'rtl');
                    document.body.classList.add('rtl-layout');
                } else {
                    document.documentElement.setAttribute('dir', 'ltr');
                    document.body.classList.remove('rtl-layout');
                }

                // Reload the page to apply language changes
                window.location.reload();
            })
            .catch(error => {
                console.error('Language switch failed:', error);

                // Restore original state
                this.innerHTML = originalHtml;
                visitorLanguageSwitches.forEach(function(el) {
                    el.classList.remove('disabled');
                    el.style.pointerEvents = 'auto';
                });

                // Show error message
                alert('@lang("Language switch failed. Please try again.")');
            });
        });
    });
});
</script>
@endguest

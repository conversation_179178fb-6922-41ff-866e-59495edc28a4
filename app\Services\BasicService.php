<?php

namespace App\Services;

use App\Http\Traits\Notify;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Image;

class BasicService
{
    use Notify;

    public function validateImage(object $getImage, string $path)
    {
        if ($getImage->getClientOriginalExtension() == 'jpg' or $getImage->getClientOriginalName() == 'jpeg' or $getImage->getClientOriginalName() == 'png') {
            $image = uniqid() . '.' . $getImage->getClientOriginalExtension();
        } else {
            $image = uniqid() . '.jpg';
        }
        Image::make($getImage->getRealPath())->resize(300, 250)->save($path . $image);
        return $image;
    }

    public function validateDate(string $date)
    {
        if (preg_match("/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4}$/", $date)) {
            return true;
        } else {
            return false;
        }
    }

    public function validateKeyword(string $search, string $keyword)
    {
        return preg_match('~' . preg_quote($search, '~') . '~i', $keyword);
    }

    public function cryptoQR($wallet, $amount, $crypto = null, $includeAmount = true)
    {
        if ($includeAmount) {
            $varb = $wallet . "?amount=" . $amount;
        } else {
            $varb = $wallet; // Only include the wallet address
        }
        //return "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=$varb&choe=UTF-8";
		return "https://quickchart.io/qr?text=$varb&size=300x300";
    }

    public function preparePaymentUpgradation($order)
    {
        $basic = (object) config('basic');
        $gateway = $order->gateway;


        if ($order->status == 0) {
            $order['status'] = 1;
            $order->update();

            $user = $order->user;

            if($order->sendmoney){
                $sendmoney = $order->sendmoney;
                $sendmoney->payment_status = 1;
                $sendmoney->paid_at = Carbon::now();
                $sendmoney->save();

                // Send SMS notification to recipient
                if (!empty($sendmoney->recipient_contact_no)) {
                    $this->sendSms($sendmoney->recipient_contact_no, 'REMITTANCE_SENT', [
                        'invoice' => $sendmoney->invoice
                    ], 4);
                }

                $msg = [
                    'username' => optional($order->user)->username,
                    'amount' => getAmount($sendmoney->totalPay, config('basic.fraction_number')),
                    'currency' => $sendmoney->send_curr
                ];
                $action = [
                    "link" => route('admin.money-transfer.details', $sendmoney),
                    "icon" => "fa fa-money-bill-alt text-white"
                ];
                $this->adminPushNotification('SEND_MONEY_REQUEST', $msg, $action);

            }else{
                $user->balance += $order->amount;
                $user->save();
            }

            $this->makeTransaction(
                $user,
                getAmount($order->amount),
                getAmount($order->charge),
                '+',
                $order->transaction,
                'Payment Via ' . $gateway->name,
                null, // No send_money_id for fund deposits
                Transaction::TYPE_FUND_DEPOSIT,
                null, // No payout_id for fund deposits
                null, // No agent_transfer_id for fund deposits
                $order->id // fund_id for idempotent protection
            );


            $msg = [
                'username' => $user->username,
                'amount' => getAmount($order->amount),
                'currency' => $basic->currency,
                'gateway' => $gateway->name
            ];
            $action = [
                "link" => route('admin.user.fundLog', $user->id),
                "icon" => "fas fa-money-bill-alt"
            ];
            $this->adminPushNotification('ADMIN_PAYMENT_COMPLETE', $msg, $action);

            $this->sendMailSms($user, 'PAYMENT_COMPLETE', [
                'gateway_name' => $gateway->name,
                'amount' => getAmount($order->amount,config('basic.fraction_number')),
                'charge' => getAmount($order->charge, config('basic.fraction_number')),
                'currency' => $basic->currency,
                'transaction' => $order->transaction
            ]);


            $msg2 = [
                'gateway_name' => $gateway->name,
                'amount' => getAmount($order->amount, config('basic.fraction_number')),
                'charge' => getAmount($order->charge, config('basic.fraction_number')),
                'currency' => $basic->currency,
                'transaction' => $order->transaction
            ];
            $action2 = [
                "link" => '#',
                "icon" => "fas fa-money-bill-alt"
            ];
            $this->userPushNotification($user, 'PAYMENT_COMPLETE', $msg2, $action2);

            session()->forget('amount');
        }
    }

    public function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^\d]/', '', $phone);

        // Remove leading zeros
        $phone = ltrim($phone, '0');

        // Add + prefix
        return '+' . $phone;
    }

    /**
     * Create a transaction record with comprehensive idempotent support
     *
     * @param $user - User object
     * @param $amount - Transaction amount
     * @param $charge - Transaction charge/fee
     * @param $trx_type - Transaction type (+ or -)
     * @param $trx_id - Transaction ID
     * @param $remarks - Transaction remarks
     * @param $send_money_id - Optional send money ID for send money operations
     * @param $transaction_type - Transaction type constant for categorization
     * @param $payout_id - Optional payout ID for withdrawal/payout operations
     * @param $agent_transfer_id - Optional agent transfer ID for agent transfer operations
     * @param $fund_id - Optional fund ID for payment gateway operations
     * @return Transaction|null - Returns the transaction or null if duplicate prevented
     */
    public function makeTransaction($user, $amount, $charge, $trx_type = null, $trx_id, $remarks = null, $send_money_id = null, $transaction_type = null, $payout_id = null, $agent_transfer_id = null, $fund_id = null)
    {
        try {
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->amount = getAmount($amount);
            $transaction->charge = $charge;
            $transaction->trx_type = $trx_type;
            $transaction->final_balance = $user->balance;
            $transaction->trx_id = $trx_id;
            $transaction->remarks = $remarks;

            // Add idempotent fields if provided
            // Set reference IDs for comprehensive idempotency
            if ($send_money_id) {
                $transaction->send_money_id = $send_money_id;
            }
            if ($payout_id) {
                $transaction->payout_id = $payout_id;
            }
            if ($agent_transfer_id) {
                $transaction->agent_transfer_id = $agent_transfer_id;
            }
            if ($fund_id) {
                $transaction->fund_id = $fund_id;
            }
            if ($transaction_type) {
                $transaction->transaction_type = $transaction_type;
            }

            $transaction->save();
            return $transaction;

        } catch (\Illuminate\Database\QueryException $e) {
            // Check for unique constraint violations (comprehensive idempotency)
            $errorMessage = $e->getMessage();
            $isDuplicateConstraint = $e->getCode() == 23000 && (
                strpos($errorMessage, 'unique_send_money_transaction_type') !== false ||
                strpos($errorMessage, 'unique_payout_transaction_type') !== false ||
                strpos($errorMessage, 'unique_agent_transfer_transaction_type') !== false ||
                strpos($errorMessage, 'unique_fund_transaction_type') !== false
            );

            if ($isDuplicateConstraint) {
                // Determine which reference ID caused the duplicate
                $referenceId = $send_money_id ?: $payout_id ?: $agent_transfer_id ?: $fund_id;
                $referenceType = $send_money_id ? 'send_money' :
                               ($payout_id ? 'payout' :
                               ($agent_transfer_id ? 'agent_transfer' : 'fund'));

                // Log the duplicate attempt for monitoring
                \Log::info('Duplicate transaction prevented by comprehensive idempotency', [
                    'user_id' => $user->id,
                    'reference_id' => $referenceId,
                    'reference_type' => $referenceType,
                    'transaction_type' => $transaction_type,
                    'trx_id' => $trx_id
                ]);

                // Return existing transaction if it exists
                $existingTransaction = null;
                if ($send_money_id && $transaction_type) {
                    $existingTransaction = Transaction::where('send_money_id', $send_money_id)
                                                    ->where('transaction_type', $transaction_type)
                                                    ->first();
                } elseif ($payout_id && $transaction_type) {
                    $existingTransaction = Transaction::where('payout_id', $payout_id)
                                                    ->where('transaction_type', $transaction_type)
                                                    ->first();
                } elseif ($agent_transfer_id && $transaction_type) {
                    $existingTransaction = Transaction::where('agent_transfer_id', $agent_transfer_id)
                                                    ->where('transaction_type', $transaction_type)
                                                    ->first();
                } elseif ($fund_id && $transaction_type) {
                    $existingTransaction = Transaction::where('fund_id', $fund_id)
                                                    ->where('transaction_type', $transaction_type)
                                                    ->first();
                }

                if ($existingTransaction) {
                    return $existingTransaction;
                }

                // Throw custom exception for better error handling
                throw new \App\Exceptions\DuplicateTransactionException(
                    "Transaction already exists for this {$referenceType} record",
                    $referenceId,
                    $transaction_type,
                    $user->id
                );
            }

            // Re-throw other database exceptions
            throw $e;
        }
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use makeTransaction with send_money_id and transaction_type for idempotent operations
     */
    public function makeTransactionLegacy($user, $amount, $charge, $trx_type = null, $trx_id, $remarks = null): void
    {
        $this->makeTransaction($user, $amount, $charge, $trx_type, $trx_id, $remarks);
    }



}

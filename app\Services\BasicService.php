<?php

namespace App\Services;

use App\Http\Traits\Notify;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Image;

class BasicService
{
    use Notify;

    public function validateImage(object $getImage, string $path)
    {
        if ($getImage->getClientOriginalExtension() == 'jpg' or $getImage->getClientOriginalName() == 'jpeg' or $getImage->getClientOriginalName() == 'png') {
            $image = uniqid() . '.' . $getImage->getClientOriginalExtension();
        } else {
            $image = uniqid() . '.jpg';
        }
        Image::make($getImage->getRealPath())->resize(300, 250)->save($path . $image);
        return $image;
    }

    public function validateDate(string $date)
    {
        if (preg_match("/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{2,4}$/", $date)) {
            return true;
        } else {
            return false;
        }
    }

    public function validateKeyword(string $search, string $keyword)
    {
        return preg_match('~' . preg_quote($search, '~') . '~i', $keyword);
    }

    public function cryptoQR($wallet, $amount, $crypto = null, $includeAmount = true)
    {
        if ($includeAmount) {
            $varb = $wallet . "?amount=" . $amount;
        } else {
            $varb = $wallet; // Only include the wallet address
        }
        //return "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=$varb&choe=UTF-8";
		return "https://quickchart.io/qr?text=$varb&size=300x300";
    }

    public function preparePaymentUpgradation($order)
    {
        $basic = (object) config('basic');
        $gateway = $order->gateway;


        if ($order->status == 0) {
            $order['status'] = 1;
            $order->update();

            $user = $order->user;

            if($order->sendmoney){
                $sendmoney = $order->sendmoney;
                $sendmoney->payment_status = 1;
                $sendmoney->paid_at = Carbon::now();
                $sendmoney->save();

                // Send SMS notification to recipient
                if (!empty($sendmoney->recipient_contact_no)) {
                    $this->sendSms($sendmoney->recipient_contact_no, 'REMITTANCE_SENT', [
                        'invoice' => $sendmoney->invoice
                    ], 4);
                }

                $msg = [
                    'username' => optional($order->user)->username,
                    'amount' => getAmount($sendmoney->totalPay, config('basic.fraction_number')),
                    'currency' => $sendmoney->send_curr
                ];
                $action = [
                    "link" => route('admin.money-transfer.details', $sendmoney),
                    "icon" => "fa fa-money-bill-alt text-white"
                ];
                $this->adminPushNotification('SEND_MONEY_REQUEST', $msg, $action);

            }else{
                $user->balance += $order->amount;
                $user->save();
            }

            $this->makeTransaction($user, getAmount($order->amount), getAmount($order->charge), $trx_type = '+',  $order->transaction, $remarks = 'Payment  Via ' . $gateway->name);


            $msg = [
                'username' => $user->username,
                'amount' => getAmount($order->amount),
                'currency' => $basic->currency,
                'gateway' => $gateway->name
            ];
            $action = [
                "link" => route('admin.user.fundLog', $user->id),
                "icon" => "fas fa-money-bill-alt"
            ];
            $this->adminPushNotification('ADMIN_PAYMENT_COMPLETE', $msg, $action);

            $this->sendMailSms($user, 'PAYMENT_COMPLETE', [
                'gateway_name' => $gateway->name,
                'amount' => getAmount($order->amount,config('basic.fraction_number')),
                'charge' => getAmount($order->charge, config('basic.fraction_number')),
                'currency' => $basic->currency,
                'transaction' => $order->transaction
            ]);


            $msg2 = [
                'gateway_name' => $gateway->name,
                'amount' => getAmount($order->amount, config('basic.fraction_number')),
                'charge' => getAmount($order->charge, config('basic.fraction_number')),
                'currency' => $basic->currency,
                'transaction' => $order->transaction
            ];
            $action2 = [
                "link" => '#',
                "icon" => "fas fa-money-bill-alt"
            ];
            $this->userPushNotification($user, 'PAYMENT_COMPLETE', $msg2, $action2);

            session()->forget('amount');
        }
    }

    public function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^\d]/', '', $phone);

        // Remove leading zeros
        $phone = ltrim($phone, '0');

        // Add + prefix
        return '+' . $phone;
    }

    /**
     * Create a transaction record with idempotent support
     *
     * @param $user
     * @param $amount
     * @param $charge
     * @param $trx_type
     * @param $trx_id
     * @param $remarks
     * @param $send_money_id - Optional send money ID for idempotent operations
     * @param $transaction_type - Optional transaction type for idempotent operations
     * @return Transaction|null - Returns the transaction or null if duplicate prevented
     */
    public function makeTransaction($user, $amount, $charge, $trx_type = null, $trx_id, $remarks = null, $send_money_id = null, $transaction_type = null)
    {
        try {
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->amount = getAmount($amount);
            $transaction->charge = $charge;
            $transaction->trx_type = $trx_type;
            $transaction->final_balance = $user->balance;
            $transaction->trx_id = $trx_id;
            $transaction->remarks = $remarks;

            // Add idempotent fields if provided
            if ($send_money_id) {
                $transaction->send_money_id = $send_money_id;
            }
            if ($transaction_type) {
                $transaction->transaction_type = $transaction_type;
            }

            $transaction->save();
            return $transaction;

        } catch (\Illuminate\Database\QueryException $e) {
            // Check if this is a unique constraint violation
            if ($e->getCode() == 23000 || strpos($e->getMessage(), 'unique_send_money_transaction_type') !== false) {
                // Log the duplicate attempt for monitoring
                \Log::info('Duplicate transaction prevented', [
                    'user_id' => $user->id,
                    'send_money_id' => $send_money_id,
                    'transaction_type' => $transaction_type,
                    'trx_id' => $trx_id
                ]);

                // Return existing transaction if it exists
                if ($send_money_id && $transaction_type) {
                    return Transaction::where('send_money_id', $send_money_id)
                                    ->where('transaction_type', $transaction_type)
                                    ->first();
                }

                // Throw custom exception for better error handling
                throw new \App\Exceptions\DuplicateTransactionException(
                    'Transaction already exists for this send money record',
                    $send_money_id,
                    $transaction_type,
                    $user->id
                );
            }

            // Re-throw other database exceptions
            throw $e;
        }
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use makeTransaction with send_money_id and transaction_type for idempotent operations
     */
    public function makeTransactionLegacy($user, $amount, $charge, $trx_type = null, $trx_id, $remarks = null): void
    {
        $this->makeTransaction($user, $amount, $charge, $trx_type, $trx_id, $remarks);
    }



}

<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Traits\Notify;
use App\Models\AgentTransfer;
use App\Models\User;
use Facades\App\Services\BasicService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use <PERSON>bauman\Purify\Facades\Purify;

class AgentTransferController extends Controller
{
    use Notify;

    public function __construct()
    {
        $this->theme = template();
        $this->middleware(['auth']);
        $this->middleware(function ($request, $next) {
            $this->user = auth()->user();
            if ($this->user->merchant == '1') {
                $this->theme .= 'merchant.';
            } else {
                $this->theme .= 'user.';
            }
            return $next($request);
        });
    }

    /**
     * Show the agent transfer form.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function showTransferForm()
    {
        $data['title'] = "Transfer to Agent";

        return view($this->theme . 'agent_transfer.form', $data);
    }

    /**
     * Process the agent transfer.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processTransfer(Request $request)
    {
        $purifiedData = Purify::clean($request->all());
        $validator = Validator::make($purifiedData, [
            'receiver_username' => 'required|string|min:5|max:50',
            'amount' => 'required|numeric|min:0',
            'note' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();

        // Find receiver by username
        $receiver = User::where('username', $purifiedData['receiver_username'])
            ->where('merchant', 1)
            ->where('status', 1)
            ->where('id', '!=', $user->id)
            ->first();

        // Check if receiver exists and is an active agent
        if (!$receiver) {
            return back()->with('error', 'Agent not found. Please verify the username and try again.')->withInput();
        }

        $basicControl = basicControl();
        $amount = $purifiedData['amount'];

        // Check minimum and maximum transfer limits
        if ($amount < $basicControl->agent_transfer_min_amount) {
            return back()->with('error', 'Minimum transfer amount is ' . getAmount($basicControl->agent_transfer_min_amount) . ' ' . $basicControl->currency)->withInput();
        }

        if ($amount > $basicControl->agent_transfer_max_amount) {
            return back()->with('error', 'Maximum transfer amount is ' . getAmount($basicControl->agent_transfer_max_amount) . ' ' . $basicControl->currency)->withInput();
        }

        // Check if user has sufficient balance
        if ($user->balance < $amount) {
            return back()->with('error', 'Insufficient balance')->withInput();
        }

        // Calculate fee
        $fixedFee = $basicControl->agent_transfer_fixed_fee;
        $percentageFee = ($amount * $basicControl->agent_transfer_percentage_fee) / 100;
        $totalFee = $fixedFee + $percentageFee;
        $finalAmount = $amount - $totalFee;

        if ($finalAmount <= 0) {
            return back()->with('error', 'Transfer amount is too small to cover the fees')->withInput();
        }

        DB::beginTransaction();
        try {
            // Create transfer record
            $trx_id = strRandom();
            $transfer = new AgentTransfer();
            $transfer->sender_id = $user->id;
            $transfer->receiver_id = $receiver->id;
            $transfer->amount = $amount;
            $transfer->fee = $totalFee;
            $transfer->final_amount = $finalAmount;
            $transfer->note = $purifiedData['note'] ?? null;
            $transfer->trx_id = $trx_id;
            $transfer->status = 1; // Completed
            $transfer->save();

            // Update sender's balance
            $user->balance -= $amount;
            $user->save();

            // Create transaction record for sender
            $senderTrxRemark = 'Transfer to agent @' . $receiver->username;
            BasicService::makeTransaction(
                $user,
                $amount,
                $totalFee,
                '-',
                $trx_id,
                $senderTrxRemark,
                null, // No send_money_id for agent transfers
                \App\Models\Transaction::TYPE_AGENT_TRANSFER_DEBIT,
                null, // No payout_id for agent transfers
                $transfer->id // agent_transfer_id for idempotent protection
            );

            // Update receiver's balance
            $receiver->balance += $finalAmount;
            $receiver->save();

            // Create transaction record for receiver
            $receiverTrxRemark = 'Received from agent @' . $user->username;
            BasicService::makeTransaction(
                $receiver,
                $finalAmount,
                0,
                '+',
                $trx_id,
                $receiverTrxRemark,
                null, // No send_money_id for agent transfers
                \App\Models\Transaction::TYPE_AGENT_TRANSFER_CREDIT,
                null, // No payout_id for agent transfers
                $transfer->id // agent_transfer_id for idempotent protection
            );

            DB::commit();

            // Send notifications
            $this->sendTransferNotification($user, $receiver, $amount, $finalAmount, $trx_id);

            return redirect()->route('user.agent.transfer.history')->with('success', 'Transfer completed successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Something went wrong: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Show transfer history.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function transferHistory()
    {
        $data['title'] = "Transfer History";
        $data['sent_transfers'] = AgentTransfer::with('receiver')
            ->where('sender_id', Auth::id())
            ->latest()
            ->paginate(config('basic.paginate'));

        $data['received_transfers'] = AgentTransfer::with('sender')
            ->where('receiver_id', Auth::id())
            ->latest()
            ->paginate(config('basic.paginate'));

        return view($this->theme . 'agent_transfer.history', $data);
    }

    /**
     * Validate agent username via AJAX.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateUsername(Request $request)
    {
        $username = $request->input('username');

        if (empty($username) || strlen($username) < 5) {
            return response()->json([
                'valid' => false,
                'message' => 'Username must be at least 5 characters long.'
            ]);
        }

        $user = Auth::user();
        $agent = User::where('username', $username)
            ->where('merchant', 1)
            ->where('status', 1)
            ->where('id', '!=', $user->id)
            ->first();

        if ($agent) {
            return response()->json([
                'valid' => true,
                'message' => 'Agent found: ' . $agent->fullname,
                'agent' => [
                    'id' => $agent->id,
                    'username' => $agent->username,
                    'fullname' => $agent->fullname
                ]
            ]);
        } else {
            return response()->json([
                'valid' => false,
                'message' => 'Agent not found. Please verify the username and try again.'
            ]);
        }
    }

    /**
     * Send notifications to both sender and receiver.
     *
     * @param User $sender
     * @param User $receiver
     * @param float $amount
     * @param float $finalAmount
     * @param string $trx_id
     * @return void
     */
    private function sendTransferNotification($sender, $receiver, $amount, $finalAmount, $trx_id)
    {
        $currentCurrency = basicControl()->currency;


        // Notify sender
        $this->sendMailSms(
            $sender,
            'AGENT_TRANSFER_SENT', // Assuming you have a template key for agent transfer sent
            [
                'amount' => getAmount($amount),
                'currency' => $currentCurrency,
                'receiver_username' => $receiver->username,
                'trx' => $trx_id,
                'balance' => getAmount($sender->balance),
            ],
            'Agent Transfer Sent'
        );

        $this->userPushNotification($sender, 'AGENT_TRANSFER_SENT',
        [
            'amount' => getAmount($amount),
            'currency' => $currentCurrency,
            'receiver_username' => $receiver->username,
            'trx' => $trx_id,
            'balance' => getAmount($sender->balance),
        ],
        [
                "link" => route('user.agent.transfer.history'),
                "icon" => "fa fa-money-bill-alt "
        ]);

        // Notify receiver
        $this->sendMailSms(
            $receiver,
            'AGENT_TRANSFER_RECEIVED', // Assuming you have a template key for agent transfer received
            [
                'amount' => getAmount($finalAmount),
                'currency' => $currentCurrency,
                'sender_username' => $sender->username,
                'trx' => $trx_id,
                'balance' => getAmount($receiver->balance),
            ],
            'Agent Transfer Received'
        );

        $this->userPushNotification($receiver, 'AGENT_TRANSFER_RECEIVED',
        [
            'amount' => getAmount($finalAmount),
            'currency' => $currentCurrency,
            'sender_username' => $sender->username,
            'trx' => $trx_id,
            'balance' => getAmount($receiver->balance),
        ],
        [
                "link" => route('user.agent.transfer.history'),
                "icon" => "fa fa-money-bill-alt "
        ]
        );
    }
}

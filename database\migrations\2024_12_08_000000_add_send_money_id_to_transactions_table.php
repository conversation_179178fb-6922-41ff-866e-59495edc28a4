<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSendMoneyIdToTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Add send_money_id foreign key column
            $table->bigInteger('send_money_id')->nullable()->after('user_id');
            
            // Add transaction_type column to distinguish between different types of transactions
            // Types: 'debit', 'credit', 'commission'
            $table->string('transaction_type', 20)->nullable()->after('send_money_id');
            
            // Add foreign key constraint
            $table->foreign('send_money_id')->references('id')->on('send_money')->onDelete('cascade');
            
            // Add unique composite index to prevent duplicate transactions of the same type for the same send money record
            // This enforces idempotency at the database level
            $table->unique(['send_money_id', 'transaction_type'], 'unique_send_money_transaction_type');
            
            // Add index for better query performance
            $table->index(['send_money_id', 'user_id'], 'idx_send_money_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Drop indexes first
            $table->dropUnique('unique_send_money_transaction_type');
            $table->dropIndex('idx_send_money_user');
            
            // Drop foreign key constraint
            $table->dropForeign(['send_money_id']);
            
            // Drop columns
            $table->dropColumn(['send_money_id', 'transaction_type']);
        });
    }
}
